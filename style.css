:root {
    /* 基于example.html的现代化配色方案 */
    --primary-bg: #F0F2F5; /* 整体页面背景 */
    --card-bg: #FFFFFF; /* 卡片背景 */
    --brand-color: #3B82F6; /* 品牌强调色 - 蓝色 */
    --brand-color-light: #60A5FA; /* 品牌强调色 - 浅蓝色 */
    --text-color-dark: #333333; /* 深色文本 */
    --text-color-medium: #555555; /* 中等文本 */
    --text-color-light: #888888; /* 浅色文本 */
    --border-color: #E0E0E0; /* 边框颜色 */
    --shadow-light: rgba(0, 0, 0, 0.08); /* 轻微阴影 */
    --focus-ring: #bfdbfe; /* 焦点环颜色 */
    --warning-color: #EF4444; /* 警告颜色 */
    --success-color: #22C55E; /* 成功颜色 */

    /* 兼容性别名 - 保持向后兼容 */
    --primary-color: var(--brand-color);
    --sidebar-bg: var(--primary-bg);
    --chat-bg: var(--card-bg);
    --text-color: var(--text-color-dark);
    --subtle-text-color: var(--text-color-light);
    --user-msg-bg: var(--brand-color);
    --ai-msg-bg: #f1f3f4;
    --box-shadow: 0 10px 30px var(--shadow-light);

    /* 响应式变量 */
    --sidebar-width: 260px;
    --mobile-header-height: 60px;
    --desktop-padding: 20px;
    --mobile-padding: 12px;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Noto Sans SC', sans-serif;
    line-height: 1.6;
    margin: 0;
    background-color: var(--primary-bg);
    color: var(--text-color-dark);
    height: 100vh;
    overflow: hidden;
}

/* 桌面端布局 */
#app-container, #api-key-view {
    width: 100%;
    height: 100vh;
    background-color: var(--chat-bg);
    display: flex;
    overflow: hidden;
}

/* 桌面端容器样式 */
@media (min-width: 769px) {
    body {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: var(--desktop-padding);
    }

    #app-container, #api-key-view {
        max-width: 1200px;
        height: calc(100vh - 40px);
        max-height: 900px;
        border-radius: 16px;
        box-shadow: var(--box-shadow);
    }
}

/* API Key 输入界面 */
#api-key-view {
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow-y: auto;
}

@media (min-width: 769px) {
    #api-key-view {
        padding: 40px;
    }
}

/* Main Container for the Setup Interface */
.main-setup-container {
    background-color: var(--card-bg);
    border-radius: 16px;
    box-shadow: 0 10px 30px var(--shadow-light);
    padding: 20px;
    width: 100%;
    max-width: 700px;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 10px 0;
}

.main-setup-header {
    margin-bottom: 15px;
}

.main-setup-header .logo {
    font-size: 44px;
    color: var(--brand-color);
    margin-bottom: 12px;
    line-height: 1;
    display: block;
}

.main-setup-header h1 {
    font-size: 2em;
    font-weight: 700;
    color: var(--text-color-dark);
    margin-bottom: 8px;
}

.main-setup-header p {
    font-size: 1em;
    color: var(--text-color-medium);
    max-width: 500px;
    margin: 0 auto;
}

/* Common Section Styling */
.setup-section {
    background-color: var(--primary-bg);
    border-radius: 12px;
    padding: 20px;
    text-align: left;
    border: 1px solid var(--border-color);
}

.setup-section h2 {
    font-size: 1.4em;
    font-weight: 600;
    color: var(--text-color-dark);
    margin-bottom: 18px;
    border-bottom: 1px dashed var(--border-color);
    padding-bottom: 12px;
}

/* Model Provider Selection - Grid for responsiveness */
.radio-group {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.radio-group label {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.95em;
    color: var(--text-color-dark);
    transition: background-color 0.15s ease;
    user-select: none;
}

.radio-group label:hover {
    border-color: var(--brand-color-light);
    background-color: rgba(59, 130, 246, 0.05);
}

.radio-group input[type="radio"] {
    margin-right: 8px;
    transform: scale(1);
    accent-color: var(--brand-color);
}

.radio-group input[type="radio"]:checked + span {
    font-weight: 600;
}

/* API Key Input Section */
.api-key-input-group {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: var(--card-bg);
    margin-top: 15px;
}

.api-key-input-group:focus-within {
    border-color: var(--brand-color-light);
    box-shadow: 0 0 0 4px var(--focus-ring);
}

.api-key-input-group input[type="password"],
.api-key-input-group input[type="text"] {
    flex-grow: 1;
    padding: 12px 15px;
    border: none;
    background: none;
    font-size: 0.95em;
    outline: none;
    color: var(--text-color-dark);
    border-radius: 10px;
}

.api-key-input-group .toggle-visibility-btn {
    background: none;
    border: none;
    padding: 0 12px;
    cursor: pointer;
    color: var(--text-color-light);
    font-size: 18px;
    transition: color 0.2s ease;
    flex-shrink: 0;
    line-height: 1;
}

.api-key-input-group .toggle-visibility-btn:hover {
    color: var(--text-color-dark);
}

.api-key-input-group .toggle-visibility-btn svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

.api-key-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 10px;
    gap: 8px;
}

.api-key-link {
    font-size: 0.85em;
    color: var(--brand-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: color 0.2s ease;
    line-height: 1;
}

.api-key-link:hover {
    color: var(--brand-color-light);
    text-decoration: underline;
}

.api-key-link svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

.api-key-warning {
    color: var(--warning-color);
    font-size: 0.75em;
    flex-grow: 1;
    text-align: left;
}

.control-buttons-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.control-buttons-group button {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s ease;
    box-shadow: 0 4px 10px rgba(0,0,0,0.08);
}

.test-connection-btn {
    background-color: #6B7280;
    color: white;
}

.test-connection-btn:hover {
    background-color: #4B5563;
}

.test-connection-btn:disabled {
    background-color: #D1D5DB;
    cursor: not-allowed;
    box-shadow: none;
}

.start-app-btn {
    background-color: var(--brand-color);
    color: white;
}

.start-app-btn:hover {
    background-color: var(--brand-color-light);
}

.start-app-btn:disabled {
    background-color: #A0C3F2;
    cursor: not-allowed;
    box-shadow: none;
}

/* --- Media Queries for API Key Setup Interface --- */

/* 移动端API Key页面优化 */
@media (max-width: 767px) {
    #api-key-view {
        padding: 12px;
        align-items: flex-start;
        padding-top: 20px;
    }

    .main-setup-container {
        padding: 16px;
        gap: 16px;
        margin: 0;
        border-radius: 12px;
        min-height: auto;
    }

    .main-setup-header .logo {
        font-size: 36px;
        margin-bottom: 8px;
    }

    .main-setup-header h1 {
        font-size: 1.5em;
        margin-bottom: 6px;
    }

    .main-setup-header p {
        font-size: 0.9em;
        line-height: 1.4;
    }

    .setup-section {
        padding: 16px;
        border-radius: 10px;
    }

    .setup-section h2 {
        font-size: 1.2em;
        margin-bottom: 12px;
    }

    .radio-group {
        gap: 8px;
    }

    .radio-option {
        padding: 12px 16px;
        font-size: 14px;
    }

    .input-group input {
        padding: 12px 16px;
        font-size: 16px; /* 防止iOS缩放 */
    }

    .btn-primary {
        padding: 14px 24px;
        font-size: 16px;
    }
}

@media (min-width: 768px) {
    .main-setup-container {
        padding: 40px;
        gap: 30px;
    }

    .main-setup-header .logo {
        font-size: 48px;
    }

    .main-setup-header h1 {
        font-size: 2.5em;
    }

    .main-setup-header p {
        font-size: 1.1em;
    }

    .setup-section {
        padding: 25px;
    }

    .setup-section h2 {
        font-size: 1.6em;
    }

    /* Model Provider Selection: 2 or 3 columns on larger screens */
    .radio-group {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }

    .radio-group label {
        padding: 12px 18px;
        font-size: 1.05em;
    }

    .radio-group input[type="radio"] {
        transform: scale(1.1);
    }

    .api-key-input-group input[type="password"],
    .api-key-input-group input[type="text"] {
        padding: 15px 20px;
        font-size: 1em;
    }

    .api-key-input-group .toggle-visibility-btn {
        padding: 0 15px;
        font-size: 20px;
    }

    .api-key-input-group .toggle-visibility-btn svg {
        width: 22px;
        height: 22px;
    }

    .api-key-info {
        flex-direction: row;
        align-items: center;
        gap: 10px;
    }

    .api-key-link {
        font-size: 0.95em;
    }

    .api-key-link svg {
        width: 16px;
        height: 16px;
    }

    .api-key-warning {
        font-size: 0.85em;
        text-align: right;
    }

    .control-buttons-group {
        flex-direction: row;
        gap: 15px;
        margin-top: 25px;
    }

    .control-buttons-group button {
        padding: 14px 25px;
        font-size: 1.1em;
    }
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(51, 51, 51, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 3000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    max-width: 90%;
    text-align: center;
}

.toast-notification.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-10px);
}

/* --- Sidebar --- */
#sidebar {
    width: var(--sidebar-width);
    background-color: var(--card-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 16px;
    transition: transform 0.3s ease;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
}

/* 移动端侧边栏 */
@media (max-width: 768px) {
    #sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 280px;
        transform: translateX(-100%);
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform;
    }

    #sidebar.mobile-open {
        transform: translateX(0);
    }

    /* 移动端遮罩层 */
    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}
#new-chat-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 14px 16px;
    background-color: var(--brand-color);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

#new-chat-button:hover {
    background-color: var(--brand-color-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

#new-chat-button svg {
    width: 18px;
    height: 18px;
}

#chat-list { list-style: none; padding: 0; margin: 0; overflow-y: auto; flex-grow: 1; }
#chat-list::-webkit-scrollbar { width: 4px; }
#chat-list::-webkit-scrollbar-thumb { background: #ccc; border-radius: 2px; }

#chat-list .chat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
    border: 1px solid transparent;
}

#chat-list .chat-item:hover {
    background-color: var(--primary-bg);
    border-color: var(--border-color);
}

#chat-list .chat-item.active-chat {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: var(--brand-color);
    color: var(--brand-color);
    font-weight: 600;
}

.chat-title {
    flex-grow: 1; cursor: pointer; white-space: nowrap;
    overflow: hidden; text-overflow: ellipsis; padding-right: 8px;
}

.delete-chat-btn {
    background: none; border: none; cursor: pointer; color: var(--subtle-text-color);
    padding: 4px; border-radius: 4px; display: flex; align-items: center; justify-content: center;
    transition: all 0.2s ease; opacity: 0; flex-shrink: 0;
}
.chat-item:hover .delete-chat-btn { opacity: 1; }
.delete-chat-btn:hover { background-color: #f1f3f4; color: #d93025; }
.delete-chat-btn svg { width: 16px; height: 16px; }

/* 设置按钮 */
.settings-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 12px 16px;
    background-color: var(--primary-bg);
    color: var(--text-color-medium);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-top: auto;
    margin-bottom: 0;
    transition: all 0.2s ease;
}

.settings-btn:hover {
    background-color: var(--card-bg);
    color: var(--text-color-dark);
    border-color: var(--brand-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.settings-btn svg {
    width: 16px;
    height: 16px;
}

@media (max-width: 768px) {
    .settings-btn span {
        display: none;
    }

    .settings-btn {
        justify-content: center;
        padding: 12px;
    }
}

/* --- Main Chat Area --- */
#chat-view-container {
    display: none;
    flex-direction: column;
    height: 100%;
    flex-grow: 1;
}

header {
    background-color: var(--card-bg);
    color: var(--text-color-dark);
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 600;
    flex-shrink: 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

#chat-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    text-align: center;
}

/* 移动端菜单按钮 */
#mobile-menu-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color-dark);
    padding: 10px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: absolute;
    left: 15px;
}

#mobile-menu-button:hover {
    background-color: var(--primary-bg);
    color: var(--brand-color);
}

#mobile-menu-button svg {
    width: 20px;
    height: 20px;
}

.header-spacer {
    width: 36px;
    position: absolute;
    right: 15px;
}

/* 响应式显示/隐藏 */
.mobile-only {
    display: none;
}

/* 移动端菜单按钮 */
#mobile-menu-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color-dark);
    padding: 10px;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
}

#mobile-menu-button:hover {
    background-color: var(--primary-bg);
}

#mobile-menu-button svg {
    width: 22px;
    height: 22px;
}

@media (max-width: 768px) {
    .mobile-only {
        display: flex;
    }

    header {
        justify-content: space-between;
        padding: 20px 16px;
        min-height: 64px;
    }

    #chat-title {
        text-align: center;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        max-width: calc(100% - 100px);
        font-size: 18px;
    }
}

#chatbox {
    flex-grow: 1;
    padding: var(--desktop-padding);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

@media (max-width: 768px) {
    #chatbox {
        padding: var(--mobile-padding);
    }
}

/* Welcome Screen */
#welcome-screen {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 40px 20px;
}

#welcome-screen .welcome-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--brand-color), var(--brand-color-light));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

#welcome-screen .welcome-logo svg {
    width: 40px;
    height: 40px;
}

#welcome-screen h2 {
    margin: 0 0 12px 0;
    font-weight: 700;
    font-size: 28px;
    color: var(--text-color-dark);
}

#welcome-screen p {
    color: var(--text-color-medium);
    margin-top: 0;
    font-size: 16px;
    line-height: 1.6;
}
.prompt-examples {
    display: flex;
    gap: 12px;
    margin-top: 32px;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 600px;
}

.prompt-example {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    padding: 14px 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    text-align: center;
    color: var(--text-color-dark);
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.prompt-example:hover {
    border-color: var(--brand-color);
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
}

@media (max-width: 768px) {
    #welcome-screen {
        padding: 24px 16px;
    }

    .prompt-examples {
        flex-direction: column;
        gap: 10px;
        margin-top: 20px;
    }

    .prompt-example {
        padding: 16px 20px;
        font-size: 15px;
        width: 100%;
        max-width: 320px;
    }

    #welcome-screen h2 {
        font-size: 24px;
        margin-bottom: 10px;
    }

    #welcome-screen p {
        font-size: 15px;
        margin-bottom: 20px;
    }

    #welcome-screen .welcome-logo {
        width: 64px;
        height: 64px;
        margin-bottom: 20px;
    }

    #welcome-screen .welcome-logo svg {
        width: 32px;
        height: 32px;
    }
}

/* Messages */
.message-wrapper {
    display: flex;
    gap: 15px;
    max-width: 90%;
    margin-bottom: 20px;
}
.message-wrapper.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}
.message-wrapper.ai {
    align-self: flex-start;
}

@media (max-width: 768px) {
    .message-wrapper {
        max-width: 95%;
        gap: 12px;
        margin-bottom: 18px;
    }

    .avatar {
        width: 32px;
        height: 32px;
    }

    .avatar svg {
        width: 18px;
        height: 18px;
    }

    .user-message {
        padding: 12px 16px;
        border-radius: 18px 6px 18px 18px;
    }

    .ai-message {
        border-radius: 6px 18px 18px 18px;
    }
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 2px solid var(--border-color);
}

.avatar svg {
    width: 20px;
    height: 20px;
    color: var(--brand-color);
}

.user .avatar {
    background: linear-gradient(135deg, var(--brand-color), var(--brand-color-light));
    color: white;
    border-color: var(--brand-color);
}

.message {
    padding: 1px 20px;
    line-height: 1.7;
    word-wrap: break-word;
}

.user-message {
    background: linear-gradient(135deg, var(--brand-color), var(--brand-color-light));
    color: white;
    border-radius: 20px 8px 20px 20px;
    padding: 14px 20px;
    box-shadow: 0 2px 12px rgba(59, 130, 246, 0.2);
}

.ai-message {
    background-color: var(--card-bg);
    color: var(--text-color-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px 20px 20px 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.error-message { background-color: #fce8e6; color: #c5221f; align-self: center; text-align: center; font-size: 14px; font-weight: 500; padding: 12px 18px; border-radius: 12px; margin-bottom: 20px; }
.thinking { align-self: flex-start; color: var(--subtle-text-color); font-style: italic; margin-left: 47px; margin-bottom: 20px; }

/* Markdown Styles */
.ai-message pre { background-color: #202124; color: #e8eaed; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: "Roboto Mono", monospace; }
.ai-message code { font-family: "Roboto Mono", monospace; background-color: rgba(0,0,0,0.08); padding: 2px 5px; border-radius: 4px; }
.ai-message p:first-child { margin-top: 12px; } .ai-message p:last-child { margin-bottom: 12px; }

/* Input Area */
#input-area {
    display: flex;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
    align-items: flex-end;
    gap: 12px;
    background-color: var(--card-bg);
}

#input-wrapper {
    flex-grow: 1;
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}

#user-input {
    flex: 1;
    padding: 16px 20px;
    border: 2px solid var(--border-color);
    border-radius: 24px;
    font-size: 16px;
    resize: none;
    max-height: 150px;
    overflow-y: auto;
    background-color: var(--card-bg);
    line-height: 1.5;
    color: var(--text-color-dark);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

#user-input:focus {
    outline: none;
    border-color: var(--brand-color);
    background-color: var(--card-bg);
    box-shadow: 0 0 0 4px var(--focus-ring);
}

#send-button {
    background: linear-gradient(135deg, var(--brand-color), var(--brand-color-light));
    color: white;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    flex-shrink: 0;
}

#send-button:hover:not(:disabled) {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

#send-button:disabled {
    background: #bdc1c6;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

#send-button svg {
    width: 18px;
    height: 18px;
}

@media (max-width: 768px) {
    #input-area {
        padding: 16px;
        gap: 8px;
    }

    #input-wrapper {
        gap: 8px;
    }

    #user-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 14px 18px;
        border-radius: 20px;
    }

    #send-button {
        width: 36px;
        height: 36px;
    }

    #send-button svg {
        width: 18px;
        height: 18px;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    #app-container, #api-key-view {
        max-width: 95vw;
        height: calc(100vh - 20px);
        margin: 10px;
    }

    #sidebar {
        width: 240px;
    }

    #chatbox {
        padding: 16px;
    }

    .message-wrapper {
        max-width: 92%;
    }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
    #app-container, #api-key-view {
        max-width: 1400px;
    }

    #sidebar {
        width: 300px;
    }

    #chatbox {
        padding: 30px;
    }

    .message-wrapper {
        max-width: 85%;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .delete-chat-btn {
        opacity: 1; /* 在触摸设备上始终显示删除按钮 */
    }

    .prompt-example {
        padding: 14px 18px;
        font-size: 16px;
    }

    #user-input {
        font-size: 16px; /* 防止iOS Safari缩放 */
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --subtle-text-color: #000000;
    }
}

/* 现代化交互效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* 消息动画 */
.message-wrapper {
    animation: fadeInUp 0.3s ease-out;
}

.message-wrapper.user {
    animation: fadeInRight 0.3s ease-out;
}

.message-wrapper.ai {
    animation: fadeInLeft 0.3s ease-out;
}

/* 聊天项动画 */
.chat-item {
    animation: fadeInUp 0.2s ease-out;
}

/* 加载动画 */
.thinking {
    position: relative;
    overflow: hidden;
}

.thinking::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.1),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

/* 按钮点击效果 */
button:active {
    opacity: 0.9;
}

/* 输入框聚焦动画 */
#user-input:focus {
    animation: pulse 0.3s ease-out;
}

/* 侧边栏滑入动画 - 使用transform过渡而不是animation避免冲突 */

/* 模态框动画增强 */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    #sidebar {
        transition: none;
    }

    .message-wrapper,
    .chat-item,
    .thinking::after {
        animation: none;
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: var(--card-bg);
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    max-width: 520px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    transform: scale(0.98);
    transition: transform 0.2s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 28px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-bg);
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-color-dark);
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color-light);
    padding: 10px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: var(--card-bg);
    color: var(--text-color-dark);
}

.close-btn svg {
    width: 20px;
    height: 20px;
}

.modal-content {
    padding: 28px;
}

.setting-group {
    margin-bottom: 20px;
}

/* API地址组与API提供商组的间距更小 */
#api-url-group {
    margin-top: -8px;
    margin-bottom: 24px;
}

.setting-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--text-color-dark);
    font-size: 15px;
}

.setting-group input {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 15px;
    background-color: var(--card-bg);
    color: var(--text-color-dark);
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.setting-group input:focus {
    outline: none;
    border-color: var(--brand-color);
    background-color: var(--card-bg);
    box-shadow: 0 0 0 4px var(--focus-ring);
}

.setting-hint {
    display: block;
    margin-top: 8px;
    font-size: 13px;
    color: var(--text-color-light);
    line-height: 1.5;
}

/* 自定义选择器 */
.custom-select {
    position: relative;
    width: 100%;
}

.select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 16px;
    background-color: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    cursor: pointer;
    transition: border-color 0.15s ease;
    user-select: none;
}

.select-trigger:hover {
    border-color: var(--brand-color-light);
}

.select-trigger.active {
    border-color: var(--brand-color);
    box-shadow: 0 0 0 3px var(--focus-ring);
}

.select-value {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color-dark);
}

.select-arrow {
    width: 20px;
    height: 20px;
    color: var(--text-color-light);
    transition: transform 0.15s ease;
}

.select-trigger.active .select-arrow {
    transform: rotate(180deg);
}

.select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    box-shadow: 0 4px 12px var(--shadow-light);
    z-index: 1000;
    margin-top: 4px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.15s ease;
}

.select-options.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.select-option {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.1s ease;
    border-bottom: 1px solid var(--border-color);
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover {
    background-color: var(--primary-bg);
}

.select-option.selected {
    background-color: var(--brand-color);
    color: white;
}

.option-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.option-name {
    font-size: 15px;
    font-weight: 500;
}

.option-desc {
    font-size: 13px;
    opacity: 0.7;
}

.provider-help {
    margin-top: 8px;
    text-align: right;
}

.provider-link {
    color: var(--brand-color);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.provider-link:hover {
    color: var(--brand-color-light);
    text-decoration: underline;
}

/* 自定义模式样式 */
.setting-group.custom-mode .setting-hint {
    color: var(--warning-color);
    font-weight: 500;
}

.setting-group.custom-mode input[required] {
    border-color: var(--warning-color);
}

.setting-group.custom-mode input[required]:focus {
    border-color: var(--warning-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* API Key输入包装器 */
.api-key-input-wrapper {
    position: relative;
}

.toggle-visibility-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color-light);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toggle-visibility-btn:hover {
    color: var(--text-color-dark);
    background-color: var(--primary-bg);
}

.toggle-visibility-btn svg {
    width: 20px;
    height: 20px;
}

/* 自定义API地址输入组 */
.custom-api-url-group {
    margin-top: 16px;
    padding: 16px;
    background-color: #FFF7ED;
    border: 2px solid #FB923C;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.custom-api-url-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #FB923C;
    border-radius: 8px;
    font-size: 16px;
    background-color: #FFFFFF;
    color: var(--text-color-dark);
    transition: all 0.2s ease;
}

.custom-api-url-group input:focus {
    outline: none;
    border-color: #EA580C;
    box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.1);
}

.custom-api-hint {
    display: block;
    margin-top: 8px;
    font-size: 13px;
    color: #EA580C;
    font-weight: 500;
}

.modal-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 28px;
}

.primary-btn, .secondary-btn, .danger-btn {
    padding: 14px 24px;
    border: none;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.primary-btn {
    background: linear-gradient(135deg, var(--brand-color), var(--brand-color-light));
    color: white;
    flex: 1;
}

.primary-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.secondary-btn {
    background-color: var(--primary-bg);
    color: var(--text-color-dark);
    border: 2px solid var(--border-color);
    flex: 1;
}

.secondary-btn:hover {
    background-color: var(--card-bg);
    border-color: var(--brand-color);
    transform: translateY(-1px);
}

.danger-zone {
    border-top: 2px solid var(--border-color);
    padding-top: 24px;
    margin-top: 8px;
}

.danger-zone h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    color: var(--warning-color);
    font-weight: 700;
}

.danger-zone p {
    margin: 0 0 20px 0;
    font-size: 14px;
    color: var(--text-color-medium);
    line-height: 1.6;
}

.danger-btn {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--warning-color);
    border: 2px solid rgba(239, 68, 68, 0.3);
    width: 100%;
    justify-content: center;
    font-weight: 600;
}

.danger-btn:hover {
    background-color: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.danger-btn svg {
    width: 16px;
    height: 16px;
}

@media (max-width: 768px) {
    .modal {
        width: 95%;
        margin: 20px;
        max-height: calc(100vh - 40px);
    }

    .modal-header {
        padding: 16px 20px 12px;
    }

    .modal-content {
        padding: 20px;
    }

    .modal-actions {
        flex-direction: column;
    }

    .primary-btn, .secondary-btn {
        flex: none;
    }
}

/* 图片上传功能样式 */
.image-upload-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background-color: #6b7280;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.image-upload-btn:hover {
    background-color: #4b5563;
    box-shadow: 0 4px 16px rgba(107, 114, 128, 0.4);
    transform: scale(1.05);
}

.image-upload-btn svg {
    width: 20px;
    height: 20px;
}

#image-preview-container {
    padding: 12px 0 0 0;
    border-top: 1px solid var(--border-color);
    margin-top: 12px;
    position: relative;
}

#image-preview-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    max-height: 120px;
    overflow-y: auto;
}

.image-preview-item {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--border-color);
    background-color: var(--card-bg);
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-remove {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ff4757;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.image-preview-remove:hover {
    background-color: #ff3838;
    transform: scale(1.1);
}

.clear-images-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background-color: rgba(255, 71, 87, 0.9);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.clear-images-btn:hover {
    background-color: #ff4757;
    transform: scale(1.1);
}

.clear-images-btn svg {
    width: 12px;
    height: 12px;
}

/* 消息中的图片样式 */
.message-images {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.message-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
    border: 1px solid var(--border-color);
}

.message-image:hover {
    transform: scale(1.02);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .image-upload-btn {
        width: 36px;
        height: 36px;
        box-shadow: 0 1px 4px rgba(107, 114, 128, 0.3);
    }

    .image-upload-btn:hover {
        box-shadow: 0 2px 8px rgba(107, 114, 128, 0.4);
    }

    .image-upload-btn svg {
        width: 18px;
        height: 18px;
    }

    /* 移动端图片预览优化 */
    #image-preview-container {
        max-height: 120px;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .image-preview-item {
        min-width: 80px;
        height: 80px;
    }

    .image-preview-item img {
        width: 80px;
        height: 80px;
    }

    .image-preview-item {
        width: 60px;
        height: 60px;
    }

    .message-image {
        max-width: 150px;
        max-height: 150px;
    }
}
